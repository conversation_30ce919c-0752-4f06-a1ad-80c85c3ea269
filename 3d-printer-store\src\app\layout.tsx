import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: '3D Printer Store - Professional 3D Printing Solutions',
  description: 'Discover the latest 3D printers, filaments, and accessories. Professional-grade 3D printing solutions for makers, professionals, and businesses.',
  keywords: '3D printer, 3D printing, filament, PLA, ABS, PETG, resin printer, FDM printer',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <div className="min-h-screen flex flex-col">
          <Header />
          <main className="flex-1">
            {children}
          </main>
          <Footer />
        </div>
      </body>
    </html>
  )
}
