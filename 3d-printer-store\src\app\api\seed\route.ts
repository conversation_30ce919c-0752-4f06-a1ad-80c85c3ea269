import { NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase'

const sampleProducts = [
  // 3D Printers
  {
    name: 'Prusa i3 MK3S+',
    description: 'The Original Prusa i3 MK3S+ is the latest version of our award-winning 3D printer. Based on the Original Prusa i3 MK3S, the MK3S+ features a redesigned extruder for better print quality.',
    price: 749.00,
    category: 'printers',
    brand: 'Prusa',
    image_url: 'https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?w=600&h=600&fit=crop',
    stock_quantity: 25,
    specifications: {
      'Print Volume': '250 × 210 × 210 mm',
      'Layer Height': '0.05 - 0.35 mm',
      'Nozzle Diameter': '0.4 mm',
      'Filament Diameter': '1.75 mm',
      'Print Speed': 'Up to 200 mm/s',
      'Connectivity': 'USB, SD card, Ethernet'
    },
    is_featured: true
  },
  {
    name: 'Ender 3 V2',
    description: 'The Ender 3 V2 is an upgraded version of the popular Ender 3 3D printer. It features a new 32-bit silent motherboard, improved user interface, and enhanced print quality.',
    price: 259.00,
    category: 'printers',
    brand: 'Creality',
    image_url: 'https://images.unsplash.com/photo-1606706842584-8e0e4b3b5b8a?w=600&h=600&fit=crop',
    stock_quantity: 50,
    specifications: {
      'Print Volume': '220 × 220 × 250 mm',
      'Layer Height': '0.1 - 0.4 mm',
      'Nozzle Diameter': '0.4 mm',
      'Filament Diameter': '1.75 mm',
      'Print Speed': 'Up to 180 mm/s',
      'Connectivity': 'USB, SD card'
    },
    is_featured: true
  },
  {
    name: 'Ultimaker S3',
    description: 'The Ultimaker S3 is a professional 3D printer designed for reliable, industrial-quality prints. Perfect for prototyping and small-scale production.',
    price: 4995.00,
    category: 'printers',
    brand: 'Ultimaker',
    image_url: 'https://images.unsplash.com/photo-1612198188060-c7c2a3b66eae?w=600&h=600&fit=crop',
    stock_quantity: 8,
    specifications: {
      'Print Volume': '230 × 190 × 200 mm',
      'Layer Height': '0.06 - 0.6 mm',
      'Nozzle Diameter': '0.25 - 0.8 mm',
      'Filament Diameter': '2.85 mm',
      'Print Speed': 'Up to 300 mm/s',
      'Connectivity': 'WiFi, Ethernet, USB'
    },
    is_featured: false
  },
  // Filaments
  {
    name: 'PLA+ Filament - White',
    description: 'High-quality PLA+ filament with improved strength and durability. Perfect for beginners and general-purpose printing.',
    price: 24.99,
    category: 'filaments',
    brand: 'SUNLU',
    image_url: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=600&h=600&fit=crop',
    stock_quantity: 100,
    specifications: {
      'Material': 'PLA+',
      'Diameter': '1.75mm',
      'Weight': '1kg',
      'Print Temperature': '190-220°C',
      'Bed Temperature': '50-60°C'
    },
    is_featured: false
  },
  {
    name: 'PETG Filament - Transparent',
    description: 'Crystal clear PETG filament with excellent chemical resistance and durability. Great for functional parts and containers.',
    price: 29.99,
    category: 'filaments',
    brand: 'Overture',
    image_url: 'https://images.unsplash.com/photo-1558618047-3c8c76ca7d13?w=600&h=600&fit=crop',
    stock_quantity: 75,
    specifications: {
      'Material': 'PETG',
      'Diameter': '1.75mm',
      'Weight': '1kg',
      'Print Temperature': '220-250°C',
      'Bed Temperature': '70-80°C'
    },
    is_featured: false
  },
  // Accessories
  {
    name: 'Nozzle Set - 0.2mm to 1.0mm',
    description: 'Complete set of brass nozzles for various printing applications. Includes 0.2mm, 0.4mm, 0.6mm, 0.8mm, and 1.0mm nozzles.',
    price: 15.99,
    category: 'accessories',
    brand: 'E3D',
    image_url: 'https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?w=600&h=600&fit=crop',
    stock_quantity: 200,
    specifications: {
      'Material': 'Brass',
      'Thread': 'M6',
      'Compatibility': 'Most hotends',
      'Quantity': '5 pieces'
    },
    is_featured: false
  }
]

export async function POST() {
  try {
    if (!supabaseAdmin) {
      return NextResponse.json(
        { error: 'Supabase admin client not available' },
        { status: 500 }
      )
    }

    console.log('Starting database seeding...')

    // Check if products already exist
    const { data: existingProducts, error: checkError } = await supabaseAdmin
      .from('products')
      .select('id')
      .limit(1)

    if (checkError) {
      console.error('Error checking existing products:', checkError)
      return NextResponse.json(
        { error: 'Failed to check existing products', details: checkError },
        { status: 500 }
      )
    }

    if (existingProducts && existingProducts.length > 0) {
      return NextResponse.json(
        { message: 'Database already contains products. Seeding skipped.' },
        { status: 200 }
      )
    }

    // Insert sample products
    const { data, error } = await supabaseAdmin
      .from('products')
      .insert(sampleProducts)
      .select()

    if (error) {
      console.error('Error inserting products:', error)
      return NextResponse.json(
        { error: 'Failed to insert products', details: error },
        { status: 500 }
      )
    }

    console.log(`Successfully inserted ${sampleProducts.length} products`)

    return NextResponse.json({
      message: 'Database seeding completed successfully!',
      productsInserted: sampleProducts.length,
      products: data
    })

  } catch (error) {
    console.error('Error seeding database:', error)
    return NextResponse.json(
      { error: 'Unexpected error during seeding', details: error },
      { status: 500 }
    )
  }
}
