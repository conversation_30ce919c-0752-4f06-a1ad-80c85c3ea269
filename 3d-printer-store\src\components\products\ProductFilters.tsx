'use client'

import { useState } from 'react'
import { ChevronDown, ChevronUp, X } from 'lucide-react'
import { Product, ProductFilters as FilterType } from '@/types'

interface ProductFiltersProps {
  filters: FilterType
  onFiltersChange: (filters: FilterType) => void
  products: Product[]
}

export default function ProductFilters({ filters, onFiltersChange, products }: ProductFiltersProps) {
  const [expandedSections, setExpandedSections] = useState({
    category: true,
    brand: true,
    price: true,
  })

  // Extract unique values from products
  const categories = [...new Set(products.map(p => p.category))].sort()
  const brands = [...new Set(products.map(p => p.brand))].sort()
  const prices = products.map(p => p.price)
  const minPrice = Math.min(...prices)
  const maxPrice = Math.max(...prices)

  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }))
  }

  const handleCategoryChange = (category: string) => {
    const newCategory = filters.category === category ? undefined : category
    onFiltersChange({ ...filters, category: newCategory })
  }

  const handleBrandChange = (brand: string) => {
    const newBrand = filters.brand === brand ? undefined : brand
    onFiltersChange({ ...filters, brand: newBrand })
  }

  const handlePriceChange = (min: number, max: number) => {
    onFiltersChange({ 
      ...filters, 
      minPrice: min === minPrice ? undefined : min,
      maxPrice: max === maxPrice ? undefined : max
    })
  }

  const clearFilters = () => {
    onFiltersChange({})
  }

  const hasActiveFilters = Object.values(filters).some(value => value !== undefined)

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Filters</h3>
        {hasActiveFilters && (
          <button
            onClick={clearFilters}
            className="text-sm text-primary-600 hover:text-primary-700 flex items-center"
          >
            <X className="w-4 h-4 mr-1" />
            Clear All
          </button>
        )}
      </div>

      <div className="space-y-6">
        {/* Category Filter */}
        <div>
          <button
            onClick={() => toggleSection('category')}
            className="flex items-center justify-between w-full text-left"
          >
            <h4 className="text-sm font-medium text-gray-900">Category</h4>
            {expandedSections.category ? (
              <ChevronUp className="w-4 h-4 text-gray-500" />
            ) : (
              <ChevronDown className="w-4 h-4 text-gray-500" />
            )}
          </button>
          
          {expandedSections.category && (
            <div className="mt-3 space-y-2">
              {categories.map((category) => {
                const categorySlug = category.toLowerCase().replace(/\s+/g, '-')
                const isSelected = filters.category === categorySlug
                const count = products.filter(p => p.category === category).length
                
                return (
                  <label key={category} className="flex items-center">
                    <input
                      type="checkbox"
                      checked={isSelected}
                      onChange={() => handleCategoryChange(categorySlug)}
                      className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">
                      {category} ({count})
                    </span>
                  </label>
                )
              })}
            </div>
          )}
        </div>

        {/* Brand Filter */}
        <div>
          <button
            onClick={() => toggleSection('brand')}
            className="flex items-center justify-between w-full text-left"
          >
            <h4 className="text-sm font-medium text-gray-900">Brand</h4>
            {expandedSections.brand ? (
              <ChevronUp className="w-4 h-4 text-gray-500" />
            ) : (
              <ChevronDown className="w-4 h-4 text-gray-500" />
            )}
          </button>
          
          {expandedSections.brand && (
            <div className="mt-3 space-y-2">
              {brands.map((brand) => {
                const isSelected = filters.brand === brand
                const count = products.filter(p => p.brand === brand).length
                
                return (
                  <label key={brand} className="flex items-center">
                    <input
                      type="checkbox"
                      checked={isSelected}
                      onChange={() => handleBrandChange(brand)}
                      className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">
                      {brand} ({count})
                    </span>
                  </label>
                )
              })}
            </div>
          )}
        </div>

        {/* Price Filter */}
        <div>
          <button
            onClick={() => toggleSection('price')}
            className="flex items-center justify-between w-full text-left"
          >
            <h4 className="text-sm font-medium text-gray-900">Price Range</h4>
            {expandedSections.price ? (
              <ChevronUp className="w-4 h-4 text-gray-500" />
            ) : (
              <ChevronDown className="w-4 h-4 text-gray-500" />
            )}
          </button>
          
          {expandedSections.price && (
            <div className="mt-3 space-y-4">
              <div className="flex space-x-2">
                <div className="flex-1">
                  <label className="block text-xs text-gray-500 mb-1">Min</label>
                  <input
                    type="number"
                    min={minPrice}
                    max={maxPrice}
                    value={filters.minPrice || minPrice}
                    onChange={(e) => handlePriceChange(Number(e.target.value), filters.maxPrice || maxPrice)}
                    className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
                  />
                </div>
                <div className="flex-1">
                  <label className="block text-xs text-gray-500 mb-1">Max</label>
                  <input
                    type="number"
                    min={minPrice}
                    max={maxPrice}
                    value={filters.maxPrice || maxPrice}
                    onChange={(e) => handlePriceChange(filters.minPrice || minPrice, Number(e.target.value))}
                    className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
                  />
                </div>
              </div>
              
              {/* Price Range Presets */}
              <div className="space-y-1">
                <button
                  onClick={() => handlePriceChange(0, 500)}
                  className="block w-full text-left text-sm text-gray-600 hover:text-primary-600 py-1"
                >
                  Under $500
                </button>
                <button
                  onClick={() => handlePriceChange(500, 1000)}
                  className="block w-full text-left text-sm text-gray-600 hover:text-primary-600 py-1"
                >
                  $500 - $1,000
                </button>
                <button
                  onClick={() => handlePriceChange(1000, 3000)}
                  className="block w-full text-left text-sm text-gray-600 hover:text-primary-600 py-1"
                >
                  $1,000 - $3,000
                </button>
                <button
                  onClick={() => handlePriceChange(3000, maxPrice)}
                  className="block w-full text-left text-sm text-gray-600 hover:text-primary-600 py-1"
                >
                  Over $3,000
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
