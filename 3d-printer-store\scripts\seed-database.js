const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

const sampleProducts = [
  // 3D Printers
  {
    name: 'Prusa i3 MK3S+',
    description: 'The Original Prusa i3 MK3S+ is the latest version of our award-winning 3D printer. Based on the Original Prusa i3 MK3S, the MK3S+ features a redesigned extruder for better print quality.',
    price: 749.00,
    category: 'printers',
    brand: 'Prusa',
    image_url: 'https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?w=600&h=600&fit=crop',
    stock_quantity: 25,
    specifications: {
      'Print Volume': '250 × 210 × 210 mm',
      'Layer Height': '0.05 - 0.35 mm',
      'Nozzle Diameter': '0.4 mm',
      'Filament Diameter': '1.75 mm',
      'Print Speed': 'Up to 200 mm/s',
      'Connectivity': 'USB, SD card, Ethernet'
    },
    features: ['Auto bed leveling', 'Filament sensor', 'Power panic', 'Magnetic bed'],
    is_featured: true
  },
  {
    name: 'Ender 3 V2',
    description: 'The Creality Ender 3 V2 is an upgraded version of the popular Ender 3 3D printer. It features a new 32-bit motherboard, improved user interface, and enhanced print quality.',
    price: 259.00,
    category: 'printers',
    brand: 'Creality',
    image_url: 'https://images.unsplash.com/photo-1606986628253-4e6e4c0e7b8a?w=600&h=600&fit=crop',
    stock_quantity: 50,
    specifications: {
      'Print Volume': '220 × 220 × 250 mm',
      'Layer Height': '0.1 - 0.4 mm',
      'Nozzle Diameter': '0.4 mm',
      'Filament Diameter': '1.75 mm',
      'Print Speed': 'Up to 180 mm/s',
      'Connectivity': 'USB, SD card'
    },
    features: ['Glass bed', 'Belt tensioners', 'Tool drawer', 'Resume printing'],
    is_featured: true
  },
  {
    name: 'Ultimaker S3',
    description: 'The Ultimaker S3 is a professional desktop 3D printer designed for reliable, industrial-quality results. Perfect for prototyping and small-scale production.',
    price: 3495.00,
    category: 'printers',
    brand: 'Ultimaker',
    image_url: 'https://images.unsplash.com/photo-1612198188060-c7c2a3b66eae?w=600&h=600&fit=crop',
    stock_quantity: 15,
    specifications: {
      'Print Volume': '230 × 190 × 200 mm',
      'Layer Height': '0.06 - 0.6 mm',
      'Nozzle Diameter': '0.25 - 1.4 mm',
      'Filament Diameter': '2.85 mm',
      'Print Speed': 'Up to 300 mm/s',
      'Connectivity': 'WiFi, Ethernet, USB'
    },
    features: ['Dual extrusion', 'Auto bed leveling', 'Air filtration', 'Cloud connectivity'],
    is_featured: true
  },
  {
    name: 'Formlabs Form 3',
    description: 'The Form 3 is a professional stereolithography (SLA) 3D printer that delivers high-resolution prints with smooth surface finish and fine details.',
    price: 3499.00,
    category: 'printers',
    brand: 'Formlabs',
    image_url: 'https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?w=600&h=600&fit=crop',
    stock_quantity: 12,
    specifications: {
      'Print Volume': '145 × 145 × 185 mm',
      'Layer Height': '0.025 - 0.3 mm',
      'Laser Spot Size': '85 microns',
      'Print Technology': 'Low Force Stereolithography (LFS)',
      'Connectivity': 'WiFi, Ethernet, USB'
    },
    features: ['Resin tank', 'Build platform', 'Wash and cure stations', 'PreForm software'],
    is_featured: false
  },

  // Filaments
  {
    name: 'PLA+ Filament - White',
    description: 'High-quality PLA+ filament with improved strength and durability. Perfect for beginners and general-purpose printing.',
    price: 24.99,
    category: 'filaments',
    brand: 'SUNLU',
    image_url: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=600&h=600&fit=crop',
    stock_quantity: 100,
    specifications: {
      'Material': 'PLA+',
      'Diameter': '1.75mm',
      'Weight': '1kg',
      'Print Temperature': '190-220°C',
      'Bed Temperature': '50-60°C',
      'Tolerance': '±0.02mm'
    },
    features: ['Easy to print', 'Low odor', 'Biodegradable', 'Good layer adhesion'],
    is_featured: false
  },
  {
    name: 'PETG Filament - Transparent',
    description: 'Crystal clear PETG filament combining the ease of PLA with the durability of ABS. Chemical resistant and food safe.',
    price: 29.99,
    category: 'filaments',
    brand: 'Overture',
    image_url: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=600&h=600&fit=crop',
    stock_quantity: 75,
    specifications: {
      'Material': 'PETG',
      'Diameter': '1.75mm',
      'Weight': '1kg',
      'Print Temperature': '220-250°C',
      'Bed Temperature': '70-80°C',
      'Tolerance': '±0.02mm'
    },
    features: ['Chemical resistant', 'Food safe', 'High clarity', 'Strong layer adhesion'],
    is_featured: false
  },
  {
    name: 'ABS Filament - Black',
    description: 'Professional grade ABS filament for functional parts requiring high strength and temperature resistance.',
    price: 27.99,
    category: 'filaments',
    brand: 'Hatchbox',
    image_url: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=600&h=600&fit=crop',
    stock_quantity: 80,
    specifications: {
      'Material': 'ABS',
      'Diameter': '1.75mm',
      'Weight': '1kg',
      'Print Temperature': '220-250°C',
      'Bed Temperature': '80-100°C',
      'Tolerance': '±0.02mm'
    },
    features: ['High strength', 'Temperature resistant', 'Chemical resistant', 'Post-processable'],
    is_featured: false
  },

  // Accessories
  {
    name: 'Glass Print Bed',
    description: 'Borosilicate glass print bed for improved first layer adhesion and easy print removal. Compatible with most FDM printers.',
    price: 19.99,
    category: 'accessories',
    brand: 'Creality',
    image_url: 'https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?w=600&h=600&fit=crop',
    stock_quantity: 60,
    specifications: {
      'Material': 'Borosilicate Glass',
      'Size': '235 × 235 × 4mm',
      'Temperature Range': '-40°C to 500°C',
      'Surface': 'Ultra-flat',
      'Compatibility': 'Ender 3, Ender 3 Pro, Ender 3 V2'
    },
    features: ['Easy print removal', 'Flat surface', 'Heat resistant', 'Easy to clean'],
    is_featured: false
  },
  {
    name: 'Nozzle Set - 0.2mm to 1.0mm',
    description: 'Complete set of hardened steel nozzles for different printing applications. Includes 0.2, 0.3, 0.4, 0.6, 0.8, and 1.0mm nozzles.',
    price: 15.99,
    category: 'accessories',
    brand: 'E3D',
    image_url: 'https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?w=600&h=600&fit=crop',
    stock_quantity: 45,
    specifications: {
      'Material': 'Hardened Steel',
      'Thread': 'M6',
      'Compatibility': 'V6 Hotend',
      'Sizes': '0.2, 0.3, 0.4, 0.6, 0.8, 1.0mm',
      'Temperature': 'Up to 500°C'
    },
    features: ['Abrasive resistant', 'High temperature', 'Precision machined', 'Easy installation'],
    is_featured: false
  },
  {
    name: 'Flexible Build Plate',
    description: 'Magnetic flexible build plate for easy print removal. Simply flex the plate to pop off your prints.',
    price: 34.99,
    category: 'accessories',
    brand: 'PrimaCreator',
    image_url: 'https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?w=600&h=600&fit=crop',
    stock_quantity: 35,
    specifications: {
      'Size': '220 × 220mm',
      'Thickness': '0.5mm',
      'Material': 'Spring Steel + PEI',
      'Temperature Range': '-20°C to 120°C',
      'Magnetic Base': 'Included'
    },
    features: ['Easy print removal', 'Magnetic attachment', 'Flexible steel', 'PEI surface'],
    is_featured: false
  },

  // Tools
  {
    name: '3D Printer Tool Kit',
    description: 'Complete tool kit for 3D printer maintenance and print removal. Includes scrapers, hex keys, nozzle cleaning tools, and more.',
    price: 22.99,
    category: 'tools',
    brand: 'REPTOR',
    image_url: 'https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?w=600&h=600&fit=crop',
    stock_quantity: 40,
    specifications: {
      'Kit Contents': '25 pieces',
      'Material': 'Stainless Steel',
      'Storage': 'Carrying case included',
      'Compatibility': 'Universal'
    },
    features: ['Complete tool set', 'Carrying case', 'Stainless steel', 'Universal compatibility'],
    is_featured: false
  }
]

async function seedDatabase() {
  try {
    console.log('Starting database seeding...')

    // Clear existing products
    console.log('Clearing existing products...')
    const { error: deleteError } = await supabase
      .from('products')
      .delete()
      .neq('id', '00000000-0000-0000-0000-000000000000') // Delete all

    if (deleteError) {
      console.error('Error clearing products:', deleteError)
      return
    }

    // Insert sample products
    console.log('Inserting sample products...')
    const { data, error } = await supabase
      .from('products')
      .insert(sampleProducts)

    if (error) {
      console.error('Error inserting products:', error)
      return
    }

    console.log(`Successfully inserted ${sampleProducts.length} products`)
    console.log('Database seeding completed!')

  } catch (error) {
    console.error('Error seeding database:', error)
  }
}

// Run the seeding function
seedDatabase()
