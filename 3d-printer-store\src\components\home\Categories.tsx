import Link from 'next/link'
import Image from 'next/image'
import { ArrowRight } from 'lucide-react'

export default function Categories() {
  const categories = [
    {
      name: 'FDM Printers',
      description: 'Fused Deposition Modeling printers for versatile printing',
      image: 'https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?w=400&h=300&fit=crop',
      href: '/products?category=fdm-printers',
      products: '120+ Products',
    },
    {
      name: 'Resin Printers',
      description: 'High-precision SLA/DLP printers for detailed models',
      image: 'https://images.unsplash.com/photo-1612198188060-c7c2a3b66eae?w=400&h=300&fit=crop',
      href: '/products?category=resin-printers',
      products: '45+ Products',
    },
    {
      name: 'Filaments',
      description: 'Premium quality filaments in various materials',
      image: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop',
      href: '/products?category=filaments',
      products: '200+ Products',
    },
    {
      name: 'Accessories',
      description: 'Tools, parts, and upgrades for your 3D printer',
      image: 'https://images.unsplash.com/photo-1565191999001-551c187427bb?w=400&h=300&fit=crop',
      href: '/products?category=accessories',
      products: '150+ Products',
    },
  ]

  return (
    <section className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
            Shop by Category
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Find exactly what you need for your 3D printing projects. From entry-level to professional-grade equipment.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {categories.map((category) => (
            <Link
              key={category.name}
              href={category.href}
              className="group bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100 hover:border-primary-200"
            >
              <div className="relative h-48 overflow-hidden">
                <Image
                  src={category.image}
                  alt={category.name}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
              </div>
              
              <div className="p-6">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="text-xl font-semibold text-gray-900 group-hover:text-primary-600 transition-colors">
                    {category.name}
                  </h3>
                  <ArrowRight className="w-5 h-5 text-gray-400 group-hover:text-primary-600 group-hover:translate-x-1 transition-all" />
                </div>
                
                <p className="text-gray-600 text-sm mb-3">
                  {category.description}
                </p>
                
                <div className="text-sm text-primary-600 font-medium">
                  {category.products}
                </div>
              </div>
            </Link>
          ))}
        </div>

        {/* Featured Brands */}
        <div className="mt-16 text-center">
          <h3 className="text-lg font-semibold text-gray-900 mb-8">Featured Brands</h3>
          <div className="flex flex-wrap justify-center items-center gap-8 opacity-60">
            <div className="text-2xl font-bold text-gray-700">Prusa</div>
            <div className="text-2xl font-bold text-gray-700">Ultimaker</div>
            <div className="text-2xl font-bold text-gray-700">Formlabs</div>
            <div className="text-2xl font-bold text-gray-700">Creality</div>
            <div className="text-2xl font-bold text-gray-700">Bambu Lab</div>
            <div className="text-2xl font-bold text-gray-700">Anycubic</div>
          </div>
        </div>
      </div>
    </section>
  )
}
