/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/seed/route";
exports.ids = ["app/api/seed/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fseed%2Froute&page=%2Fapi%2Fseed%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fseed%2Froute.ts&appDir=F%3A%5Caguent%5C3d-printer-store%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5Caguent%5C3d-printer-store&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fseed%2Froute&page=%2Fapi%2Fseed%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fseed%2Froute.ts&appDir=F%3A%5Caguent%5C3d-printer-store%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5Caguent%5C3d-printer-store&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var F_aguent_3d_printer_store_src_app_api_seed_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/seed/route.ts */ \"(rsc)/./src/app/api/seed/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/seed/route\",\n        pathname: \"/api/seed\",\n        filename: \"route\",\n        bundlePath: \"app/api/seed/route\"\n    },\n    resolvedPagePath: \"F:\\\\aguent\\\\3d-printer-store\\\\src\\\\app\\\\api\\\\seed\\\\route.ts\",\n    nextConfigOutput,\n    userland: F_aguent_3d_printer_store_src_app_api_seed_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/seed/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fseed%2Froute&page=%2Fapi%2Fseed%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fseed%2Froute.ts&appDir=F%3A%5Caguent%5C3d-printer-store%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5Caguent%5C3d-printer-store&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/seed/route.ts":
/*!***********************************!*\
  !*** ./src/app/api/seed/route.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase */ \"(rsc)/./src/lib/supabase.ts\");\n\n\nconst sampleProducts = [\n    // 3D Printers\n    {\n        name: \"Prusa i3 MK3S+\",\n        description: \"The Original Prusa i3 MK3S+ is the latest version of our award-winning 3D printer. Based on the Original Prusa i3 MK3S, the MK3S+ features a redesigned extruder for better print quality.\",\n        price: 749.00,\n        category: \"printers\",\n        brand: \"Prusa\",\n        image_url: \"https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?w=600&h=600&fit=crop\",\n        stock_quantity: 25,\n        specifications: {\n            \"Print Volume\": \"250 \\xd7 210 \\xd7 210 mm\",\n            \"Layer Height\": \"0.05 - 0.35 mm\",\n            \"Nozzle Diameter\": \"0.4 mm\",\n            \"Filament Diameter\": \"1.75 mm\",\n            \"Print Speed\": \"Up to 200 mm/s\",\n            \"Connectivity\": \"USB, SD card, Ethernet\"\n        },\n        is_featured: true\n    },\n    {\n        name: \"Ender 3 V2\",\n        description: \"The Ender 3 V2 is an upgraded version of the popular Ender 3 3D printer. It features a new 32-bit silent motherboard, improved user interface, and enhanced print quality.\",\n        price: 259.00,\n        category: \"printers\",\n        brand: \"Creality\",\n        image_url: \"https://images.unsplash.com/photo-1606706842584-8e0e4b3b5b8a?w=600&h=600&fit=crop\",\n        stock_quantity: 50,\n        specifications: {\n            \"Print Volume\": \"220 \\xd7 220 \\xd7 250 mm\",\n            \"Layer Height\": \"0.1 - 0.4 mm\",\n            \"Nozzle Diameter\": \"0.4 mm\",\n            \"Filament Diameter\": \"1.75 mm\",\n            \"Print Speed\": \"Up to 180 mm/s\",\n            \"Connectivity\": \"USB, SD card\"\n        },\n        is_featured: true\n    },\n    {\n        name: \"Ultimaker S3\",\n        description: \"The Ultimaker S3 is a professional 3D printer designed for reliable, industrial-quality prints. Perfect for prototyping and small-scale production.\",\n        price: 4995.00,\n        category: \"printers\",\n        brand: \"Ultimaker\",\n        image_url: \"https://images.unsplash.com/photo-1612198188060-c7c2a3b66eae?w=600&h=600&fit=crop\",\n        stock_quantity: 8,\n        specifications: {\n            \"Print Volume\": \"230 \\xd7 190 \\xd7 200 mm\",\n            \"Layer Height\": \"0.06 - 0.6 mm\",\n            \"Nozzle Diameter\": \"0.25 - 0.8 mm\",\n            \"Filament Diameter\": \"2.85 mm\",\n            \"Print Speed\": \"Up to 300 mm/s\",\n            \"Connectivity\": \"WiFi, Ethernet, USB\"\n        },\n        is_featured: false\n    },\n    // Filaments\n    {\n        name: \"PLA+ Filament - White\",\n        description: \"High-quality PLA+ filament with improved strength and durability. Perfect for beginners and general-purpose printing.\",\n        price: 24.99,\n        category: \"filaments\",\n        brand: \"SUNLU\",\n        image_url: \"https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=600&h=600&fit=crop\",\n        stock_quantity: 100,\n        specifications: {\n            \"Material\": \"PLA+\",\n            \"Diameter\": \"1.75mm\",\n            \"Weight\": \"1kg\",\n            \"Print Temperature\": \"190-220\\xb0C\",\n            \"Bed Temperature\": \"50-60\\xb0C\"\n        },\n        is_featured: false\n    },\n    {\n        name: \"PETG Filament - Transparent\",\n        description: \"Crystal clear PETG filament with excellent chemical resistance and durability. Great for functional parts and containers.\",\n        price: 29.99,\n        category: \"filaments\",\n        brand: \"Overture\",\n        image_url: \"https://images.unsplash.com/photo-1558618047-3c8c76ca7d13?w=600&h=600&fit=crop\",\n        stock_quantity: 75,\n        specifications: {\n            \"Material\": \"PETG\",\n            \"Diameter\": \"1.75mm\",\n            \"Weight\": \"1kg\",\n            \"Print Temperature\": \"220-250\\xb0C\",\n            \"Bed Temperature\": \"70-80\\xb0C\"\n        },\n        is_featured: false\n    },\n    // Accessories\n    {\n        name: \"Nozzle Set - 0.2mm to 1.0mm\",\n        description: \"Complete set of brass nozzles for various printing applications. Includes 0.2mm, 0.4mm, 0.6mm, 0.8mm, and 1.0mm nozzles.\",\n        price: 15.99,\n        category: \"accessories\",\n        brand: \"E3D\",\n        image_url: \"https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?w=600&h=600&fit=crop\",\n        stock_quantity: 200,\n        specifications: {\n            \"Material\": \"Brass\",\n            \"Thread\": \"M6\",\n            \"Compatibility\": \"Most hotends\",\n            \"Quantity\": \"5 pieces\"\n        },\n        is_featured: false\n    }\n];\nasync function POST() {\n    try {\n        if (!_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabaseAdmin) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Supabase admin client not available\"\n            }, {\n                status: 500\n            });\n        }\n        console.log(\"Starting database seeding...\");\n        // Check if products already exist\n        const { data: existingProducts, error: checkError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabaseAdmin.from(\"products\").select(\"id\").limit(1);\n        if (checkError) {\n            console.error(\"Error checking existing products:\", checkError);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Failed to check existing products\",\n                details: checkError\n            }, {\n                status: 500\n            });\n        }\n        if (existingProducts && existingProducts.length > 0) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                message: \"Database already contains products. Seeding skipped.\"\n            }, {\n                status: 200\n            });\n        }\n        // Insert sample products\n        const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabaseAdmin.from(\"products\").insert(sampleProducts).select();\n        if (error) {\n            console.error(\"Error inserting products:\", error);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Failed to insert products\",\n                details: error\n            }, {\n                status: 500\n            });\n        }\n        console.log(`Successfully inserted ${sampleProducts.length} products`);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            message: \"Database seeding completed successfully!\",\n            productsInserted: sampleProducts.length,\n            products: data\n        });\n    } catch (error) {\n        console.error(\"Error seeding database:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Unexpected error during seeding\",\n            details: error\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/seed/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   supabaseAdmin: () => (/* binding */ supabaseAdmin)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\nconst supabaseUrl = \"https://gdoeaiwylzjqerksuqpr.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imdkb2VhaXd5bHpqcWVya3N1cXByIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEzMTEwMjIsImV4cCI6MjA2Njg4NzAyMn0.cEY-dCJoBxkLR4hKuLmGioELxhhrSElSP6XCSFpE8m0\";\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n// For server-side operations\nconst supabaseAdmin = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, process.env.SUPABASE_SERVICE_ROLE_KEY);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3N1cGFiYXNlLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFvRDtBQUdwRCxNQUFNQyxjQUFjQywwQ0FBb0M7QUFDeEQsTUFBTUcsa0JBQWtCSCxrTkFBeUM7QUFFMUQsTUFBTUssV0FBV1AsbUVBQVlBLENBQVdDLGFBQWFJLGlCQUFnQjtBQUU1RSw2QkFBNkI7QUFDdEIsTUFBTUcsZ0JBQWdCUixtRUFBWUEsQ0FDdkNDLGFBQ0FDLFFBQVFDLEdBQUcsQ0FBQ00seUJBQXlCLEVBQ3RDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vM2QtcHJpbnRlci1zdG9yZS8uL3NyYy9saWIvc3VwYWJhc2UudHM/MDZlMSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGVDbGllbnQgfSBmcm9tICdAc3VwYWJhc2Uvc3VwYWJhc2UtanMnXG5pbXBvcnQgeyBEYXRhYmFzZSB9IGZyb20gJ0AvdHlwZXMvZGF0YWJhc2UnXG5cbmNvbnN0IHN1cGFiYXNlVXJsID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMIVxuY29uc3Qgc3VwYWJhc2VBbm9uS2V5ID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU1VQQUJBU0VfQU5PTl9LRVkhXG5cbmV4cG9ydCBjb25zdCBzdXBhYmFzZSA9IGNyZWF0ZUNsaWVudDxEYXRhYmFzZT4oc3VwYWJhc2VVcmwsIHN1cGFiYXNlQW5vbktleSlcblxuLy8gRm9yIHNlcnZlci1zaWRlIG9wZXJhdGlvbnNcbmV4cG9ydCBjb25zdCBzdXBhYmFzZUFkbWluID0gY3JlYXRlQ2xpZW50PERhdGFiYXNlPihcbiAgc3VwYWJhc2VVcmwsXG4gIHByb2Nlc3MuZW52LlNVUEFCQVNFX1NFUlZJQ0VfUk9MRV9LRVkhXG4pXG4iXSwibmFtZXMiOlsiY3JlYXRlQ2xpZW50Iiwic3VwYWJhc2VVcmwiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMIiwic3VwYWJhc2VBbm9uS2V5IiwiTkVYVF9QVUJMSUNfU1VQQUJBU0VfQU5PTl9LRVkiLCJzdXBhYmFzZSIsInN1cGFiYXNlQWRtaW4iLCJTVVBBQkFTRV9TRVJWSUNFX1JPTEVfS0VZIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/isows"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fseed%2Froute&page=%2Fapi%2Fseed%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fseed%2Froute.ts&appDir=F%3A%5Caguent%5C3d-printer-store%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5Caguent%5C3d-printer-store&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();