# Supabase Configuration
# Replace these with your actual Supabase project credentials
# You can find these in your Supabase project settings

# Your Supabase project URL
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co

# Your Supabase anon/public key
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here

# Your Supabase service role key (for seeding database)
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here

# Development Note:
# To get your actual Supabase credentials:
# 1. Go to https://supabase.com
# 2. Create a new project or use an existing one
# 3. Go to Settings > API
# 4. Copy the Project URL and anon public key
# 5. Replace the values above with your actual credentials
