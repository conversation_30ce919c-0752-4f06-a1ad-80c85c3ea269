{"name": "3d-printer-store", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "seed": "node scripts/seed-database.js", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"next": "14.0.4", "react": "^18", "react-dom": "^18", "@supabase/supabase-js": "^2.38.5", "@supabase/auth-helpers-nextjs": "^0.8.7", "zustand": "^4.4.7", "lucide-react": "^0.294.0", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "clsx": "^2.0.0", "tailwind-merge": "^2.1.0"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "postcss": "^8", "tailwindcss": "^3.3.0", "eslint": "^8", "eslint-config-next": "14.0.4"}}