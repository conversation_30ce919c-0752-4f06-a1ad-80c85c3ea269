'use client'

import Link from 'next/link'
import Image from 'next/image'
import { Star, ShoppingCart, Heart, Eye } from 'lucide-react'
import { Product } from '@/types'
import { formatPrice } from '@/lib/utils'
import { useCartStore } from '@/store/cartStore'

interface ProductGridProps {
  products: Product[]
}

export default function ProductGrid({ products }: ProductGridProps) {
  const { addItem } = useCartStore()

  const handleAddToCart = (product: Product) => {
    addItem(product)
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      {products.map((product) => (
        <div
          key={product.id}
          className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden group"
        >
          <div className="relative h-48 overflow-hidden">
            <Image
              src={product.image_url}
              alt={product.name}
              fill
              className="object-cover group-hover:scale-105 transition-transform duration-300"
            />
            
            {/* Overlay Actions */}
            <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center space-x-2">
              <Link
                href={`/products/${product.id}`}
                className="p-2 bg-white rounded-full shadow-md hover:bg-gray-50 transition-colors"
              >
                <Eye className="w-4 h-4 text-gray-600" />
              </Link>
              <button className="p-2 bg-white rounded-full shadow-md hover:bg-gray-50 transition-colors">
                <Heart className="w-4 h-4 text-gray-600" />
              </button>
            </div>

            {/* Badges */}
            <div className="absolute top-2 left-2 space-y-1">
              {product.is_featured && (
                <span className="bg-primary-600 text-white text-xs px-2 py-1 rounded">
                  Featured
                </span>
              )}
              {product.stock_quantity < 10 && product.stock_quantity > 0 && (
                <span className="bg-orange-500 text-white text-xs px-2 py-1 rounded block">
                  Low Stock
                </span>
              )}
              {product.stock_quantity === 0 && (
                <span className="bg-red-500 text-white text-xs px-2 py-1 rounded block">
                  Out of Stock
                </span>
              )}
            </div>
          </div>

          <div className="p-4">
            {/* Brand */}
            <div className="mb-2">
              <span className="text-xs text-primary-600 font-medium uppercase tracking-wide">
                {product.brand}
              </span>
            </div>
            
            {/* Product Name */}
            <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
              <Link 
                href={`/products/${product.id}`} 
                className="hover:text-primary-600 transition-colors"
              >
                {product.name}
              </Link>
            </h3>

            {/* Category */}
            <div className="text-sm text-gray-500 mb-2">
              {product.category}
            </div>

            {/* Rating */}
            <div className="flex items-center mb-3">
              <div className="flex items-center">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className={`w-4 h-4 ${
                      i < 4 ? 'text-yellow-400 fill-current' : 'text-gray-300'
                    }`}
                  />
                ))}
              </div>
              <span className="text-sm text-gray-500 ml-2">(4.0)</span>
            </div>

            {/* Price and Actions */}
            <div className="flex items-center justify-between">
              <div className="text-xl font-bold text-gray-900">
                {formatPrice(product.price)}
              </div>
              <button
                onClick={() => handleAddToCart(product)}
                disabled={product.stock_quantity === 0}
                className="btn btn-primary p-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <ShoppingCart className="w-4 h-4" />
              </button>
            </div>

            {/* Stock Status */}
            {product.stock_quantity === 0 ? (
              <div className="mt-2 text-sm text-red-600 font-medium">
                Out of Stock
              </div>
            ) : product.stock_quantity < 10 ? (
              <div className="mt-2 text-sm text-orange-600 font-medium">
                Only {product.stock_quantity} left
              </div>
            ) : (
              <div className="mt-2 text-sm text-green-600 font-medium">
                In Stock
              </div>
            )}
          </div>
        </div>
      ))}
    </div>
  )
}
