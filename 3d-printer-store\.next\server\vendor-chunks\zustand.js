"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/zustand";
exports.ids = ["vendor-chunks/zustand"];
exports.modules = {

/***/ "(ssr)/./node_modules/zustand/esm/index.mjs":
/*!********************************************!*\
  !*** ./node_modules/zustand/esm/index.mjs ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   create: () => (/* binding */ create),\n/* harmony export */   createStore: () => (/* reexport safe */ zustand_vanilla__WEBPACK_IMPORTED_MODULE_0__.createStore),\n/* harmony export */   \"default\": () => (/* binding */ react),\n/* harmony export */   useStore: () => (/* binding */ useStore)\n/* harmony export */ });\n/* harmony import */ var zustand_vanilla__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand/vanilla */ \"(ssr)/./node_modules/zustand/esm/vanilla.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var use_sync_external_store_shim_with_selector_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! use-sync-external-store/shim/with-selector.js */ \"(ssr)/./node_modules/use-sync-external-store/shim/with-selector.js\");\n\n\n\n\nconst { useDebugValue } = react__WEBPACK_IMPORTED_MODULE_1__;\nconst { useSyncExternalStoreWithSelector } = use_sync_external_store_shim_with_selector_js__WEBPACK_IMPORTED_MODULE_2__;\nlet didWarnAboutEqualityFn = false;\nconst identity = (arg)=>arg;\nfunction useStore(api, selector = identity, equalityFn) {\n    if (( false ? 0 : void 0) !== \"production\" && equalityFn && !didWarnAboutEqualityFn) {\n        console.warn(\"[DEPRECATED] Use `createWithEqualityFn` instead of `create` or use `useStoreWithEqualityFn` instead of `useStore`. They can be imported from 'zustand/traditional'. https://github.com/pmndrs/zustand/discussions/1937\");\n        didWarnAboutEqualityFn = true;\n    }\n    const slice = useSyncExternalStoreWithSelector(api.subscribe, api.getState, api.getServerState || api.getInitialState, selector, equalityFn);\n    useDebugValue(slice);\n    return slice;\n}\nconst createImpl = (createState)=>{\n    if (( false ? 0 : void 0) !== \"production\" && typeof createState !== \"function\") {\n        console.warn(\"[DEPRECATED] Passing a vanilla store will be unsupported in a future version. Instead use `import { useStore } from 'zustand'`.\");\n    }\n    const api = typeof createState === \"function\" ? (0,zustand_vanilla__WEBPACK_IMPORTED_MODULE_0__.createStore)(createState) : createState;\n    const useBoundStore = (selector, equalityFn)=>useStore(api, selector, equalityFn);\n    Object.assign(useBoundStore, api);\n    return useBoundStore;\n};\nconst create = (createState)=>createState ? createImpl(createState) : createImpl;\nvar react = (createState)=>{\n    if (( false ? 0 : void 0) !== \"production\") {\n        console.warn(\"[DEPRECATED] Default export is deprecated. Instead use `import { create } from 'zustand'`.\");\n    }\n    return create(createState);\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/zustand/esm/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/zustand/esm/middleware.mjs":
/*!*************************************************!*\
  !*** ./node_modules/zustand/esm/middleware.mjs ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   combine: () => (/* binding */ combine),\n/* harmony export */   createJSONStorage: () => (/* binding */ createJSONStorage),\n/* harmony export */   devtools: () => (/* binding */ devtools),\n/* harmony export */   persist: () => (/* binding */ persist),\n/* harmony export */   redux: () => (/* binding */ redux),\n/* harmony export */   subscribeWithSelector: () => (/* binding */ subscribeWithSelector)\n/* harmony export */ });\nconst reduxImpl = (reducer, initial)=>(set, _get, api)=>{\n        api.dispatch = (action)=>{\n            set((state)=>reducer(state, action), false, action);\n            return action;\n        };\n        api.dispatchFromDevtools = true;\n        return {\n            dispatch: (...a)=>api.dispatch(...a),\n            ...initial\n        };\n    };\nconst redux = reduxImpl;\nconst trackedConnections = /* @__PURE__ */ new Map();\nconst getTrackedConnectionState = (name)=>{\n    const api = trackedConnections.get(name);\n    if (!api) return {};\n    return Object.fromEntries(Object.entries(api.stores).map(([key, api2])=>[\n            key,\n            api2.getState()\n        ]));\n};\nconst extractConnectionInformation = (store, extensionConnector, options)=>{\n    if (store === void 0) {\n        return {\n            type: \"untracked\",\n            connection: extensionConnector.connect(options)\n        };\n    }\n    const existingConnection = trackedConnections.get(options.name);\n    if (existingConnection) {\n        return {\n            type: \"tracked\",\n            store,\n            ...existingConnection\n        };\n    }\n    const newConnection = {\n        connection: extensionConnector.connect(options),\n        stores: {}\n    };\n    trackedConnections.set(options.name, newConnection);\n    return {\n        type: \"tracked\",\n        store,\n        ...newConnection\n    };\n};\nconst devtoolsImpl = (fn, devtoolsOptions = {})=>(set, get, api)=>{\n        const { enabled, anonymousActionType, store, ...options } = devtoolsOptions;\n        let extensionConnector;\n        try {\n            extensionConnector = (enabled != null ? enabled : ( false ? 0 : void 0) !== \"production\") && window.__REDUX_DEVTOOLS_EXTENSION__;\n        } catch (_e) {}\n        if (!extensionConnector) {\n            if (( false ? 0 : void 0) !== \"production\" && enabled) {\n                console.warn(\"[zustand devtools middleware] Please install/enable Redux devtools extension\");\n            }\n            return fn(set, get, api);\n        }\n        const { connection, ...connectionInformation } = extractConnectionInformation(store, extensionConnector, options);\n        let isRecording = true;\n        api.setState = (state, replace, nameOrAction)=>{\n            const r = set(state, replace);\n            if (!isRecording) return r;\n            const action = nameOrAction === void 0 ? {\n                type: anonymousActionType || \"anonymous\"\n            } : typeof nameOrAction === \"string\" ? {\n                type: nameOrAction\n            } : nameOrAction;\n            if (store === void 0) {\n                connection == null ? void 0 : connection.send(action, get());\n                return r;\n            }\n            connection == null ? void 0 : connection.send({\n                ...action,\n                type: `${store}/${action.type}`\n            }, {\n                ...getTrackedConnectionState(options.name),\n                [store]: api.getState()\n            });\n            return r;\n        };\n        const setStateFromDevtools = (...a)=>{\n            const originalIsRecording = isRecording;\n            isRecording = false;\n            set(...a);\n            isRecording = originalIsRecording;\n        };\n        const initialState = fn(api.setState, get, api);\n        if (connectionInformation.type === \"untracked\") {\n            connection == null ? void 0 : connection.init(initialState);\n        } else {\n            connectionInformation.stores[connectionInformation.store] = api;\n            connection == null ? void 0 : connection.init(Object.fromEntries(Object.entries(connectionInformation.stores).map(([key, store2])=>[\n                    key,\n                    key === connectionInformation.store ? initialState : store2.getState()\n                ])));\n        }\n        if (api.dispatchFromDevtools && typeof api.dispatch === \"function\") {\n            let didWarnAboutReservedActionType = false;\n            const originalDispatch = api.dispatch;\n            api.dispatch = (...a)=>{\n                if (( false ? 0 : void 0) !== \"production\" && a[0].type === \"__setState\" && !didWarnAboutReservedActionType) {\n                    console.warn('[zustand devtools middleware] \"__setState\" action type is reserved to set state from the devtools. Avoid using it.');\n                    didWarnAboutReservedActionType = true;\n                }\n                originalDispatch(...a);\n            };\n        }\n        connection.subscribe((message)=>{\n            var _a;\n            switch(message.type){\n                case \"ACTION\":\n                    if (typeof message.payload !== \"string\") {\n                        console.error(\"[zustand devtools middleware] Unsupported action format\");\n                        return;\n                    }\n                    return parseJsonThen(message.payload, (action)=>{\n                        if (action.type === \"__setState\") {\n                            if (store === void 0) {\n                                setStateFromDevtools(action.state);\n                                return;\n                            }\n                            if (Object.keys(action.state).length !== 1) {\n                                console.error(`\n                    [zustand devtools middleware] Unsupported __setState action format. \n                    When using 'store' option in devtools(), the 'state' should have only one key, which is a value of 'store' that was passed in devtools(),\n                    and value of this only key should be a state object. Example: { \"type\": \"__setState\", \"state\": { \"abc123Store\": { \"foo\": \"bar\" } } }\n                    `);\n                            }\n                            const stateFromDevtools = action.state[store];\n                            if (stateFromDevtools === void 0 || stateFromDevtools === null) {\n                                return;\n                            }\n                            if (JSON.stringify(api.getState()) !== JSON.stringify(stateFromDevtools)) {\n                                setStateFromDevtools(stateFromDevtools);\n                            }\n                            return;\n                        }\n                        if (!api.dispatchFromDevtools) return;\n                        if (typeof api.dispatch !== \"function\") return;\n                        api.dispatch(action);\n                    });\n                case \"DISPATCH\":\n                    switch(message.payload.type){\n                        case \"RESET\":\n                            setStateFromDevtools(initialState);\n                            if (store === void 0) {\n                                return connection == null ? void 0 : connection.init(api.getState());\n                            }\n                            return connection == null ? void 0 : connection.init(getTrackedConnectionState(options.name));\n                        case \"COMMIT\":\n                            if (store === void 0) {\n                                connection == null ? void 0 : connection.init(api.getState());\n                                return;\n                            }\n                            return connection == null ? void 0 : connection.init(getTrackedConnectionState(options.name));\n                        case \"ROLLBACK\":\n                            return parseJsonThen(message.state, (state)=>{\n                                if (store === void 0) {\n                                    setStateFromDevtools(state);\n                                    connection == null ? void 0 : connection.init(api.getState());\n                                    return;\n                                }\n                                setStateFromDevtools(state[store]);\n                                connection == null ? void 0 : connection.init(getTrackedConnectionState(options.name));\n                            });\n                        case \"JUMP_TO_STATE\":\n                        case \"JUMP_TO_ACTION\":\n                            return parseJsonThen(message.state, (state)=>{\n                                if (store === void 0) {\n                                    setStateFromDevtools(state);\n                                    return;\n                                }\n                                if (JSON.stringify(api.getState()) !== JSON.stringify(state[store])) {\n                                    setStateFromDevtools(state[store]);\n                                }\n                            });\n                        case \"IMPORT_STATE\":\n                            {\n                                const { nextLiftedState } = message.payload;\n                                const lastComputedState = (_a = nextLiftedState.computedStates.slice(-1)[0]) == null ? void 0 : _a.state;\n                                if (!lastComputedState) return;\n                                if (store === void 0) {\n                                    setStateFromDevtools(lastComputedState);\n                                } else {\n                                    setStateFromDevtools(lastComputedState[store]);\n                                }\n                                connection == null ? void 0 : connection.send(null, // FIXME no-any\n                                nextLiftedState);\n                                return;\n                            }\n                        case \"PAUSE_RECORDING\":\n                            return isRecording = !isRecording;\n                    }\n                    return;\n            }\n        });\n        return initialState;\n    };\nconst devtools = devtoolsImpl;\nconst parseJsonThen = (stringified, f)=>{\n    let parsed;\n    try {\n        parsed = JSON.parse(stringified);\n    } catch (e) {\n        console.error(\"[zustand devtools middleware] Could not parse the received json\", e);\n    }\n    if (parsed !== void 0) f(parsed);\n};\nconst subscribeWithSelectorImpl = (fn)=>(set, get, api)=>{\n        const origSubscribe = api.subscribe;\n        api.subscribe = (selector, optListener, options)=>{\n            let listener = selector;\n            if (optListener) {\n                const equalityFn = (options == null ? void 0 : options.equalityFn) || Object.is;\n                let currentSlice = selector(api.getState());\n                listener = (state)=>{\n                    const nextSlice = selector(state);\n                    if (!equalityFn(currentSlice, nextSlice)) {\n                        const previousSlice = currentSlice;\n                        optListener(currentSlice = nextSlice, previousSlice);\n                    }\n                };\n                if (options == null ? void 0 : options.fireImmediately) {\n                    optListener(currentSlice, currentSlice);\n                }\n            }\n            return origSubscribe(listener);\n        };\n        const initialState = fn(set, get, api);\n        return initialState;\n    };\nconst subscribeWithSelector = subscribeWithSelectorImpl;\nconst combine = (initialState, create)=>(...a)=>Object.assign({}, initialState, create(...a));\nfunction createJSONStorage(getStorage, options) {\n    let storage;\n    try {\n        storage = getStorage();\n    } catch (_e) {\n        return;\n    }\n    const persistStorage = {\n        getItem: (name)=>{\n            var _a;\n            const parse = (str2)=>{\n                if (str2 === null) {\n                    return null;\n                }\n                return JSON.parse(str2, options == null ? void 0 : options.reviver);\n            };\n            const str = (_a = storage.getItem(name)) != null ? _a : null;\n            if (str instanceof Promise) {\n                return str.then(parse);\n            }\n            return parse(str);\n        },\n        setItem: (name, newValue)=>storage.setItem(name, JSON.stringify(newValue, options == null ? void 0 : options.replacer)),\n        removeItem: (name)=>storage.removeItem(name)\n    };\n    return persistStorage;\n}\nconst toThenable = (fn)=>(input)=>{\n        try {\n            const result = fn(input);\n            if (result instanceof Promise) {\n                return result;\n            }\n            return {\n                then (onFulfilled) {\n                    return toThenable(onFulfilled)(result);\n                },\n                catch (_onRejected) {\n                    return this;\n                }\n            };\n        } catch (e) {\n            return {\n                then (_onFulfilled) {\n                    return this;\n                },\n                catch (onRejected) {\n                    return toThenable(onRejected)(e);\n                }\n            };\n        }\n    };\nconst oldImpl = (config, baseOptions)=>(set, get, api)=>{\n        let options = {\n            getStorage: ()=>localStorage,\n            serialize: JSON.stringify,\n            deserialize: JSON.parse,\n            partialize: (state)=>state,\n            version: 0,\n            merge: (persistedState, currentState)=>({\n                    ...currentState,\n                    ...persistedState\n                }),\n            ...baseOptions\n        };\n        let hasHydrated = false;\n        const hydrationListeners = /* @__PURE__ */ new Set();\n        const finishHydrationListeners = /* @__PURE__ */ new Set();\n        let storage;\n        try {\n            storage = options.getStorage();\n        } catch (_e) {}\n        if (!storage) {\n            return config((...args)=>{\n                console.warn(`[zustand persist middleware] Unable to update item '${options.name}', the given storage is currently unavailable.`);\n                set(...args);\n            }, get, api);\n        }\n        const thenableSerialize = toThenable(options.serialize);\n        const setItem = ()=>{\n            const state = options.partialize({\n                ...get()\n            });\n            let errorInSync;\n            const thenable = thenableSerialize({\n                state,\n                version: options.version\n            }).then((serializedValue)=>storage.setItem(options.name, serializedValue)).catch((e)=>{\n                errorInSync = e;\n            });\n            if (errorInSync) {\n                throw errorInSync;\n            }\n            return thenable;\n        };\n        const savedSetState = api.setState;\n        api.setState = (state, replace)=>{\n            savedSetState(state, replace);\n            void setItem();\n        };\n        const configResult = config((...args)=>{\n            set(...args);\n            void setItem();\n        }, get, api);\n        let stateFromStorage;\n        const hydrate = ()=>{\n            var _a;\n            if (!storage) return;\n            hasHydrated = false;\n            hydrationListeners.forEach((cb)=>cb(get()));\n            const postRehydrationCallback = ((_a = options.onRehydrateStorage) == null ? void 0 : _a.call(options, get())) || void 0;\n            return toThenable(storage.getItem.bind(storage))(options.name).then((storageValue)=>{\n                if (storageValue) {\n                    return options.deserialize(storageValue);\n                }\n            }).then((deserializedStorageValue)=>{\n                if (deserializedStorageValue) {\n                    if (typeof deserializedStorageValue.version === \"number\" && deserializedStorageValue.version !== options.version) {\n                        if (options.migrate) {\n                            return options.migrate(deserializedStorageValue.state, deserializedStorageValue.version);\n                        }\n                        console.error(`State loaded from storage couldn't be migrated since no migrate function was provided`);\n                    } else {\n                        return deserializedStorageValue.state;\n                    }\n                }\n            }).then((migratedState)=>{\n                var _a2;\n                stateFromStorage = options.merge(migratedState, (_a2 = get()) != null ? _a2 : configResult);\n                set(stateFromStorage, true);\n                return setItem();\n            }).then(()=>{\n                postRehydrationCallback == null ? void 0 : postRehydrationCallback(stateFromStorage, void 0);\n                hasHydrated = true;\n                finishHydrationListeners.forEach((cb)=>cb(stateFromStorage));\n            }).catch((e)=>{\n                postRehydrationCallback == null ? void 0 : postRehydrationCallback(void 0, e);\n            });\n        };\n        api.persist = {\n            setOptions: (newOptions)=>{\n                options = {\n                    ...options,\n                    ...newOptions\n                };\n                if (newOptions.getStorage) {\n                    storage = newOptions.getStorage();\n                }\n            },\n            clearStorage: ()=>{\n                storage == null ? void 0 : storage.removeItem(options.name);\n            },\n            getOptions: ()=>options,\n            rehydrate: ()=>hydrate(),\n            hasHydrated: ()=>hasHydrated,\n            onHydrate: (cb)=>{\n                hydrationListeners.add(cb);\n                return ()=>{\n                    hydrationListeners.delete(cb);\n                };\n            },\n            onFinishHydration: (cb)=>{\n                finishHydrationListeners.add(cb);\n                return ()=>{\n                    finishHydrationListeners.delete(cb);\n                };\n            }\n        };\n        hydrate();\n        return stateFromStorage || configResult;\n    };\nconst newImpl = (config, baseOptions)=>(set, get, api)=>{\n        let options = {\n            storage: createJSONStorage(()=>localStorage),\n            partialize: (state)=>state,\n            version: 0,\n            merge: (persistedState, currentState)=>({\n                    ...currentState,\n                    ...persistedState\n                }),\n            ...baseOptions\n        };\n        let hasHydrated = false;\n        const hydrationListeners = /* @__PURE__ */ new Set();\n        const finishHydrationListeners = /* @__PURE__ */ new Set();\n        let storage = options.storage;\n        if (!storage) {\n            return config((...args)=>{\n                console.warn(`[zustand persist middleware] Unable to update item '${options.name}', the given storage is currently unavailable.`);\n                set(...args);\n            }, get, api);\n        }\n        const setItem = ()=>{\n            const state = options.partialize({\n                ...get()\n            });\n            return storage.setItem(options.name, {\n                state,\n                version: options.version\n            });\n        };\n        const savedSetState = api.setState;\n        api.setState = (state, replace)=>{\n            savedSetState(state, replace);\n            void setItem();\n        };\n        const configResult = config((...args)=>{\n            set(...args);\n            void setItem();\n        }, get, api);\n        api.getInitialState = ()=>configResult;\n        let stateFromStorage;\n        const hydrate = ()=>{\n            var _a, _b;\n            if (!storage) return;\n            hasHydrated = false;\n            hydrationListeners.forEach((cb)=>{\n                var _a2;\n                return cb((_a2 = get()) != null ? _a2 : configResult);\n            });\n            const postRehydrationCallback = ((_b = options.onRehydrateStorage) == null ? void 0 : _b.call(options, (_a = get()) != null ? _a : configResult)) || void 0;\n            return toThenable(storage.getItem.bind(storage))(options.name).then((deserializedStorageValue)=>{\n                if (deserializedStorageValue) {\n                    if (typeof deserializedStorageValue.version === \"number\" && deserializedStorageValue.version !== options.version) {\n                        if (options.migrate) {\n                            return [\n                                true,\n                                options.migrate(deserializedStorageValue.state, deserializedStorageValue.version)\n                            ];\n                        }\n                        console.error(`State loaded from storage couldn't be migrated since no migrate function was provided`);\n                    } else {\n                        return [\n                            false,\n                            deserializedStorageValue.state\n                        ];\n                    }\n                }\n                return [\n                    false,\n                    void 0\n                ];\n            }).then((migrationResult)=>{\n                var _a2;\n                const [migrated, migratedState] = migrationResult;\n                stateFromStorage = options.merge(migratedState, (_a2 = get()) != null ? _a2 : configResult);\n                set(stateFromStorage, true);\n                if (migrated) {\n                    return setItem();\n                }\n            }).then(()=>{\n                postRehydrationCallback == null ? void 0 : postRehydrationCallback(stateFromStorage, void 0);\n                stateFromStorage = get();\n                hasHydrated = true;\n                finishHydrationListeners.forEach((cb)=>cb(stateFromStorage));\n            }).catch((e)=>{\n                postRehydrationCallback == null ? void 0 : postRehydrationCallback(void 0, e);\n            });\n        };\n        api.persist = {\n            setOptions: (newOptions)=>{\n                options = {\n                    ...options,\n                    ...newOptions\n                };\n                if (newOptions.storage) {\n                    storage = newOptions.storage;\n                }\n            },\n            clearStorage: ()=>{\n                storage == null ? void 0 : storage.removeItem(options.name);\n            },\n            getOptions: ()=>options,\n            rehydrate: ()=>hydrate(),\n            hasHydrated: ()=>hasHydrated,\n            onHydrate: (cb)=>{\n                hydrationListeners.add(cb);\n                return ()=>{\n                    hydrationListeners.delete(cb);\n                };\n            },\n            onFinishHydration: (cb)=>{\n                finishHydrationListeners.add(cb);\n                return ()=>{\n                    finishHydrationListeners.delete(cb);\n                };\n            }\n        };\n        if (!options.skipHydration) {\n            hydrate();\n        }\n        return stateFromStorage || configResult;\n    };\nconst persistImpl = (config, baseOptions)=>{\n    if (\"getStorage\" in baseOptions || \"serialize\" in baseOptions || \"deserialize\" in baseOptions) {\n        if (( false ? 0 : void 0) !== \"production\") {\n            console.warn(\"[DEPRECATED] `getStorage`, `serialize` and `deserialize` options are deprecated. Use `storage` option instead.\");\n        }\n        return oldImpl(config, baseOptions);\n    }\n    return newImpl(config, baseOptions);\n};\nconst persist = persistImpl;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/zustand/esm/middleware.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/zustand/esm/vanilla.mjs":
/*!**********************************************!*\
  !*** ./node_modules/zustand/esm/vanilla.mjs ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createStore: () => (/* binding */ createStore),\n/* harmony export */   \"default\": () => (/* binding */ vanilla)\n/* harmony export */ });\nconst createStoreImpl = (createState)=>{\n    let state;\n    const listeners = /* @__PURE__ */ new Set();\n    const setState = (partial, replace)=>{\n        const nextState = typeof partial === \"function\" ? partial(state) : partial;\n        if (!Object.is(nextState, state)) {\n            const previousState = state;\n            state = (replace != null ? replace : typeof nextState !== \"object\" || nextState === null) ? nextState : Object.assign({}, state, nextState);\n            listeners.forEach((listener)=>listener(state, previousState));\n        }\n    };\n    const getState = ()=>state;\n    const getInitialState = ()=>initialState;\n    const subscribe = (listener)=>{\n        listeners.add(listener);\n        return ()=>listeners.delete(listener);\n    };\n    const destroy = ()=>{\n        if (( false ? 0 : void 0) !== \"production\") {\n            console.warn(\"[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected.\");\n        }\n        listeners.clear();\n    };\n    const api = {\n        setState,\n        getState,\n        getInitialState,\n        subscribe,\n        destroy\n    };\n    const initialState = state = createState(setState, getState, api);\n    return api;\n};\nconst createStore = (createState)=>createState ? createStoreImpl(createState) : createStoreImpl;\nvar vanilla = (createState)=>{\n    if (( false ? 0 : void 0) !== \"production\") {\n        console.warn(\"[DEPRECATED] Default export is deprecated. Instead use import { createStore } from 'zustand/vanilla'.\");\n    }\n    return createStore(createState);\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/zustand/esm/vanilla.mjs\n");

/***/ })

};
;