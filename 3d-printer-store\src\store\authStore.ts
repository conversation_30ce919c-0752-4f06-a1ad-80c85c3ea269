import { create } from 'zustand'
import { User } from '@/types'
import { supabase } from '@/lib/supabase'
import { signOut as authSignOut, getCurrentUser } from '@/lib/auth'

interface AuthStore {
  user: User | null
  loading: boolean
  setUser: (user: User | null) => void
  setLoading: (loading: boolean) => void
  signOut: () => Promise<void>
  initialize: () => Promise<void>
}

export const useAuthStore = create<AuthStore>((set, get) => ({
  user: null,
  loading: true,

  setUser: (user: User | null) => {
    set({ user })
  },

  setLoading: (loading: boolean) => {
    set({ loading })
  },

  signOut: async () => {
    try {
      await authSignOut()
      set({ user: null })
    } catch (error) {
      console.error('Error signing out:', error)
    }
  },

  initialize: async () => {
    try {
      set({ loading: true })
      
      // Get initial session
      const { data: { session } } = await supabase.auth.getSession()
      
      if (session?.user) {
        const user = await getCurrentUser()
        set({ user })
      }

      // Listen for auth changes
      supabase.auth.onAuthStateChange(async (event, session) => {
        if (event === 'SIGNED_IN' && session?.user) {
          const user = await getCurrentUser()
          set({ user })
        } else if (event === 'SIGNED_OUT') {
          set({ user: null })
        }
      })
    } catch (error) {
      console.error('Error initializing auth:', error)
    } finally {
      set({ loading: false })
    }
  },
}))

// Initialize auth on store creation
if (typeof window !== 'undefined') {
  useAuthStore.getState().initialize()
}
