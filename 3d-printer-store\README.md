# 3D Printer E-commerce Store

A modern, full-featured e-commerce website specializing in 3D printers and related products. Built with Next.js, TypeScript, Tailwind CSS, and Supabase.

## Features

- **Product Catalog**: Browse and search 3D printers, filaments, accessories, and tools
- **Shopping Cart**: Add/remove items, update quantities, persistent cart state
- **User Authentication**: Sign up, sign in, password reset, user profiles
- **Checkout Process**: Complete order flow with payment integration
- **Admin Panel**: Product management, order tracking, analytics dashboard
- **Responsive Design**: Mobile-first design with modern UI/UX
- **Database Integration**: Supabase for authentication, database, and real-time features

## Tech Stack

- **Frontend**: Next.js 14, React, TypeScript
- **Styling**: Tailwind CSS
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth
- **State Management**: Zustand
- **Icons**: Lucide React
- **Testing**: Jest, React Testing Library

## Getting Started

### Prerequisites

- Node.js 18+ 
- npm or yarn
- Supabase account

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd 3d-printer-store
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   
   Create a `.env.local` file in the root directory:
   ```env
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
   ```

4. **Set up Supabase database**
   
   Run the SQL commands from `supabase/schema.sql` in your Supabase SQL editor to create the database schema.

5. **Seed the database**
   ```bash
   npm run seed
   ```

6. **Start the development server**
   ```bash
   npm run dev
   ```

   Open [http://localhost:3000](http://localhost:3000) in your browser.

## Database Schema

The application uses the following main tables:

- **users**: User profiles and authentication data
- **products**: Product catalog with specifications and features
- **orders**: Customer orders and order details
- **order_items**: Individual items within orders

## Project Structure

```
src/
├── app/                    # Next.js app directory
│   ├── admin/             # Admin panel pages
│   ├── auth/              # Authentication pages
│   ├── cart/              # Shopping cart page
│   ├── checkout/          # Checkout process
│   ├── products/          # Product pages
│   └── profile/           # User profile page
├── components/            # Reusable components
│   ├── admin/            # Admin-specific components
│   ├── auth/             # Authentication components
│   ├── cart/             # Cart components
│   ├── home/             # Homepage components
│   ├── layout/           # Layout components
│   └── products/         # Product components
├── lib/                  # Utility functions and configurations
├── store/                # Zustand state management
└── types/                # TypeScript type definitions
```

## Key Features

### Product Management
- Product listing with filtering and search
- Category-based navigation
- Product detail pages with specifications
- Featured products and recommendations

### Shopping Cart
- Add/remove products
- Quantity management
- Persistent cart state
- Order summary calculations

### User Authentication
- User registration and login
- Password reset functionality
- User profile management
- Protected routes

### Admin Panel
- Product CRUD operations
- Order management
- Sales analytics
- User management

### Checkout Process
- Shipping information collection
- Payment form (demo)
- Order confirmation
- Email notifications (planned)

## Testing

Run the test suite:

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch
```

## Deployment

### Vercel (Recommended)

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Add environment variables in Vercel dashboard
4. Deploy

### Other Platforms

The application can be deployed to any platform that supports Next.js:
- Netlify
- Railway
- DigitalOcean App Platform
- AWS Amplify

## Environment Variables

Required environment variables:

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key (for seeding)
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For support and questions:
- Create an issue in the GitHub repository
- Check the documentation
- Review the code comments

## Roadmap

- [ ] Payment integration (Stripe/PayPal)
- [ ] Email notifications
- [ ] Product reviews and ratings
- [ ] Wishlist functionality
- [ ] Advanced search filters
- [ ] Inventory management
- [ ] Multi-language support
- [ ] Mobile app (React Native)

## Acknowledgments

- Next.js team for the amazing framework
- Supabase for the backend infrastructure
- Tailwind CSS for the styling system
- Lucide for the icon library
