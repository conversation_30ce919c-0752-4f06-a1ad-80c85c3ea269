-- Insert sample 3D printer products
INSERT INTO public.products (name, description, price, image_url, category, brand, specifications, stock_quantity, is_featured) VALUES
(
  'Prusa i3 MK3S+',
  'The Original Prusa i3 MK3S+ is the latest version of our award-winning 3D printers. We have upgraded the entire electronics, improved the power supply, and implemented a number of other improvements.',
  749.00,
  'https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?w=500',
  'FDM Printers',
  'Prusa',
  '{"build_volume": "250×210×210mm", "layer_height": "0.05-0.35mm", "filament_diameter": "1.75mm", "nozzle_diameter": "0.4mm", "heated_bed": true, "auto_leveling": true}',
  15,
  true
),
(
  'Ender 3 V2',
  'The Ender 3 V2 is Creality''s newest version of their popular Ender 3 3D printer. It features several improvements including a new 32-bit silent motherboard, improved extruder, and better user interface.',
  199.00,
  'https://images.unsplash.com/photo-1606986628253-4e6bc1b0b14e?w=500',
  'FDM Printers',
  'Creality',
  '{"build_volume": "220×220×250mm", "layer_height": "0.1-0.4mm", "filament_diameter": "1.75mm", "nozzle_diameter": "0.4mm", "heated_bed": true, "auto_leveling": false}',
  25,
  true
),
(
  'Formlabs Form 3',
  'The Form 3 is a professional desktop stereolithography (SLA) 3D printer that delivers high-resolution prints with smooth surface finishes and fine details.',
  3499.00,
  'https://images.unsplash.com/photo-1612198188060-c7c2a3b66eae?w=500',
  'Resin Printers',
  'Formlabs',
  '{"build_volume": "145×145×185mm", "layer_height": "0.025-0.3mm", "technology": "SLA", "light_source": "LED", "connectivity": "Wi-Fi, Ethernet"}',
  8,
  true
),
(
  'Ultimaker S3',
  'The Ultimaker S3 is a professional 3D printer designed for reliable, industrial-quality results. Perfect for functional prototyping and small-scale production.',
  4995.00,
  'https://images.unsplash.com/photo-1565191999001-551c187427bb?w=500',
  'Professional Printers',
  'Ultimaker',
  '{"build_volume": "230×190×200mm", "layer_height": "0.06-0.6mm", "filament_diameter": "2.85mm", "dual_extrusion": true, "heated_bed": true, "auto_leveling": true}',
  5,
  false
),
(
  'Bambu Lab X1 Carbon',
  'The X1 Carbon is a flagship consumer 3D printer featuring automatic calibration, multi-color printing, and AI-powered error detection.',
  1199.00,
  'https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?w=500',
  'FDM Printers',
  'Bambu Lab',
  '{"build_volume": "256×256×256mm", "layer_height": "0.08-0.35mm", "filament_diameter": "1.75mm", "multi_color": true, "heated_bed": true, "auto_leveling": true}',
  12,
  true
),
(
  'PLA Filament - White',
  'High-quality PLA filament perfect for beginners and professionals alike. Easy to print with excellent layer adhesion.',
  24.99,
  'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=500',
  'Filaments',
  'Hatchbox',
  '{"material": "PLA", "diameter": "1.75mm", "weight": "1kg", "color": "White", "printing_temp": "190-220°C"}',
  50,
  false
),
(
  'ABS Filament - Black',
  'Durable ABS filament ideal for functional parts that require strength and flexibility.',
  29.99,
  'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=500',
  'Filaments',
  'SUNLU',
  '{"material": "ABS", "diameter": "1.75mm", "weight": "1kg", "color": "Black", "printing_temp": "220-250°C"}',
  35,
  false
),
(
  'PETG Filament - Clear',
  'Chemical resistant PETG filament combining the ease of PLA with the durability of ABS.',
  34.99,
  'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=500',
  'Filaments',
  'Overture',
  '{"material": "PETG", "diameter": "1.75mm", "weight": "1kg", "color": "Clear", "printing_temp": "220-250°C"}',
  28,
  false
),
(
  'Standard Resin - Grey',
  'High-quality photopolymer resin for detailed miniatures and prototypes.',
  49.99,
  'https://images.unsplash.com/photo-1612198188060-c7c2a3b66eae?w=500',
  'Resins',
  'Formlabs',
  '{"material": "Standard Resin", "color": "Grey", "volume": "1L", "shore_hardness": "D80"}',
  20,
  false
),
(
  'Glass Build Plate',
  'Borosilicate glass build plate for improved first layer adhesion and easy part removal.',
  39.99,
  'https://images.unsplash.com/photo-1565191999001-551c187427bb?w=500',
  'Accessories',
  'Creality',
  '{"size": "235×235×4mm", "material": "Borosilicate Glass", "compatibility": "Ender 3 Series"}',
  40,
  false
);

-- Create a sample admin user (this would typically be done through the Supabase Auth UI)
-- Note: This is just for reference - actual user creation should be done through Supabase Auth
-- INSERT INTO auth.users (id, email, encrypted_password, email_confirmed_at, created_at, updated_at)
-- VALUES ('550e8400-e29b-41d4-a716-446655440000', '<EMAIL>', crypt('admin123', gen_salt('bf')), NOW(), NOW(), NOW());

-- Insert corresponding user profile for admin
-- INSERT INTO public.users (id, email, full_name, is_admin)
-- VALUES ('550e8400-e29b-41d4-a716-446655440000', '<EMAIL>', 'Store Administrator', true);
